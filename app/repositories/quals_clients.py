import logging
from uuid import uuid4

from config import settings
from core.urls import url_join
from dependencies import CustomAsyncClient
from schemas.quals_clients import (
    ClientComprehensive,
    ClientCreateRequest,
    ClientCreateResponse,
    ClientSearchItem,
    ClientSearchRequest,
    ClientSearchResponse,
)


__all__ = ['QualsClientsRepository']


logger = logging.getLogger(__name__)


class QualsClientsRepository:
    """Repository for Quals Clients API operations."""

    def __init__(self, http_client: CustomAsyncClient):
        """
        Initialize the Quals Clients Repository with an HTTP client.

        Args:
            http_client: The CustomAsyncClient to use for requests
        """
        self._base_url = settings.quals_clients_api.base_url
        self._http_client = http_client

    def _generate_mock_search_results(self, search_request: ClientSearchRequest) -> list[ClientSearchItem]:
        """
        Generate mock search results for testing.

        Args:
            search_request: The search request parameters

        Returns:
            list[ClientSearchItem]: Mock search results
        """
        # Generate mock clients based on search query
        mock_clients = [
            ClientSearchItem(
                id='client-001',
                name=f'Global Corp {search_request.contains}',
                qualsCount=15,
                clientConfidentiality=1,
            ),
            ClientSearchItem(
                id='client-002',
                name=f'International {search_request.contains} Ltd',
                qualsCount=8,
                clientConfidentiality=1,
            ),
            ClientSearchItem(
                id='client-003',
                name=f'{search_request.contains} Industries Inc',
                qualsCount=23,
                clientConfidentiality=1,
            ),
        ]

        # Apply pagination
        start_idx = search_request.page_idx * search_request.page_size
        end_idx = start_idx + search_request.page_size
        return mock_clients[start_idx:end_idx]

    def _generate_mock_client_comprehensive(self, create_request: ClientCreateRequest) -> ClientComprehensive:
        """
        Generate a mock comprehensive client object for testing.

        Args:
            create_request: The client creation request

        Returns:
            ClientComprehensive: Mock comprehensive client data
        """
        client_id = int(uuid4().int >> 96)  # Use upper 32 bits of UUID as a unique int

        return ClientComprehensive(
            id=client_id,
            name=create_request.name,
            description=f'Mock description for {create_request.name}',
            primaryLocalIndustry='Technology',
            primaryGlobalIndustry='Information Technology',
            secondaryLocalIndustries=['Software Development', 'Consulting'],
            secondaryGlobalIndustries=['Digital Transformation', 'Cloud Services'],
        )

    async def search_clients(self, search_request: ClientSearchRequest, token: str) -> ClientSearchResponse:
        """
        Search for clients using the Quals Clients API.

        Args:
            search_request: Search parameters including query text and pagination
            token: Bearer token for authentication

        Returns:
            ClientSearchResponse: Search results with pagination info
        """
        if settings.quals_clients_api.mock_client_api_enabled:
            # Mock implementation
            logger.info('Using mock data for client search: %s', search_request.contains)
            mock_results = self._generate_mock_search_results(search_request)

            return ClientSearchResponse(
                clients=mock_results,
                total_count=len(mock_results) + 10,  # Simulate more results available
                page_size=search_request.page_size,
                page_idx=search_request.page_idx,
            )

        # Real implementation (when API is working)
        try:
            url = url_join(self._base_url, 'client/searchClients')
            params = {
                'contains': search_request.contains,
                'pageSize': search_request.page_size,
                'pageIdx': search_request.page_idx,
            }
            headers = {'Authorization': f'Bearer {token}'}

            logger.info('Searching clients with params: %s', params)
            response_data = (await self._http_client.get(url, params=params, headers=headers)).json()

            # Parse response - API returns array directly, not wrapped in object
            if isinstance(response_data, list):
                clients = [ClientSearchItem(**client) for client in response_data]
                total_count = len(clients)  # API doesn't provide total count
            else:
                clients = [ClientSearchItem(**client) for client in response_data.get('clients', [])]
                total_count = response_data.get('totalCount', len(clients))

            return ClientSearchResponse(
                clients=clients,
                total_count=total_count,
                page_size=search_request.page_size,
                page_idx=search_request.page_idx,
            )

        except Exception:
            logger.exception('Error searching clients')
            raise

    async def create_client(self, create_request: ClientCreateRequest, token: str) -> ClientCreateResponse:
        """
        Create a new client using the Quals Clients API.

        Args:
            create_request: Client data for creation
            token: Bearer token for authentication

        Returns:
            ClientCreateResponse: Created client data
        """
        if settings.quals_clients_api.mock_client_api_enabled:
            # Mock implementation
            logger.info('Using mock data for client creation: %s', create_request.name)
            mock_client = self._generate_mock_client_comprehensive(create_request)

            return ClientCreateResponse(client=mock_client, success=True, message='Client created successfully (mock)')

        # Real implementation (when API is working)
        url = url_join(self._base_url, 'clients')

        try:
            # Try query parameter approach first (as mentioned in requirements)
            params = {'name': create_request.name}
            headers = {'Authorization': f'Bearer {token}'}

            logger.info('Creating client: %s', create_request.name)
            response_data = (await self._http_client.post(url, params=params, headers=headers)).json()
            logger.info('Client created: %s', response_data)

            # Parse response and return structured data
            client = ClientComprehensive(**response_data)

            return ClientCreateResponse(client=client, success=True, message='Client created successfully')

        except Exception:
            logger.exception("Error creating client '%s'", create_request.name)
            raise
